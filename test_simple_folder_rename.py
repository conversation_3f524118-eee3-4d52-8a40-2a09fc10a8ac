#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单文件夹重命名功能的脚本
"""
import os
import tempfile
import shutil

def test_simple_folder_rename():
    """测试简单文件夹重命名功能"""
    
    # 创建临时测试目录
    test_base_dir = tempfile.mkdtemp(prefix="test_folder_rename_")
    print(f"📁 创建测试目录: {test_base_dir}")
    
    try:
        # 创建测试文件夹
        test_folders = [
            "科洛弗道10号 (2016)",
            "The.Matrix.1999",
            "复仇者联盟4：终局之战",
            "Avengers Endgame 2019",
            "肖申克的救赎.1994"
        ]
        
        created_folders = []
        for folder_name in test_folders:
            folder_path = os.path.join(test_base_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)
            
            # 在每个文件夹中创建一些测试文件
            test_file = os.path.join(folder_path, f"{folder_name}.mkv")
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write("测试视频文件")
            
            created_folders.append(folder_path)
            print(f"  ✅ 创建文件夹: {folder_name}")
        
        print(f"\n🎬 测试文件夹创建完成，共 {len(created_folders)} 个")
        
        # 模拟简单文件夹重命名逻辑
        def simulate_simple_rename(folder_path, format_template):
            """模拟简单文件夹重命名逻辑"""
            folder_name = os.path.basename(folder_path)
            
            # 简单的标题和年份提取（实际应用中会使用API）
            title = folder_name
            year = "未知年份"
            tmdbid = "N/A"
            
            # 尝试提取年份
            import re
            year_match = re.search(r'\b(19|20)\d{2}\b', folder_name)
            if year_match:
                year = year_match.group()
                title = re.sub(r'\s*\(?\b(19|20)\d{2}\b\)?\s*', '', folder_name).strip()
            
            # 清理标题
            title = re.sub(r'[._]', ' ', title).strip()
            title = re.sub(r'\s+', ' ', title)
            
            # 应用格式模板
            new_name = format_template.replace("{title}", title).replace("{year}", year)
            
            # 处理TMDB ID占位符
            if tmdbid != "N/A":
                new_name = new_name.replace("{tmdbid-{tmdbid}}", f"{{tmdbid-{tmdbid}}}")
                new_name = new_name.replace("{tmdb-{tmdbid}}", f"{{tmdb-{tmdbid}}}")
                new_name = new_name.replace("{tmdbid}", tmdbid)
            else:
                # 清理TMDB占位符
                cleanup_patterns = [
                    r'\{tmdbid-\{tmdbid\}\}', r'\{tmdb-\{tmdbid\}\}',
                    r'\{tmdbid\}', r'\{tmdb-xxxx\}', r'\{tmdbid-xxxx\}'
                ]
                for pattern in cleanup_patterns:
                    new_name = re.sub(pattern, '', new_name)
                new_name = new_name.strip()
            
            # 清理非法字符
            new_name = re.sub(r'[<>:"/\\|?*]', '_', new_name).strip(" .")
            new_name = re.sub(r'[\s\._-]+$', '', new_name)
            new_name = re.sub(r'([\s\._-])[\s\._]+', r'\1', new_name)
            
            return new_name, title, year, tmdbid
        
        # 测试不同的格式模板
        format_templates = [
            "{title} ({year}) {tmdb-{tmdbid}}",
            "{title} ({year}) [tmdbid={tmdbid}]",
            "{title} ({year})",
            "{title} - {year}",
            "[{year}] {title}"
        ]
        
        print(f"\n🔄 开始测试简单文件夹重命名...")
        print("=" * 80)
        
        for i, format_template in enumerate(format_templates, 1):
            print(f"\n📋 测试格式 {i}: {format_template}")
            print("-" * 60)
            
            for folder_path in created_folders:
                original_name = os.path.basename(folder_path)
                new_name, title, year, tmdbid = simulate_simple_rename(folder_path, format_template)
                
                print(f"  原始名称: {original_name}")
                print(f"  提取信息: 标题='{title}', 年份='{year}', TMDBID='{tmdbid}'")
                print(f"  新名称:   {new_name}")
                print(f"  状态:     {'✅ 成功' if new_name and new_name != original_name else '⚠️ 无变化'}")
                print()
        
        print("🎯 简单文件夹重命名测试完成！")
        print("\n💡 功能特点:")
        print("  - 只重命名文件夹名称，不处理内部文件")
        print("  - 支持多种TMDB ID格式")
        print("  - 自动清理非法字符")
        print("  - 可选择目标目录")
        print("  - 支持自定义格式模板")
        
    finally:
        # 清理测试目录
        try:
            shutil.rmtree(test_base_dir)
            print(f"\n🧹 清理测试目录: {test_base_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试目录失败: {e}")

if __name__ == "__main__":
    test_simple_folder_rename()
