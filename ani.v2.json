[{"id": "e107ceaa-2cc1-48ea-96e7-f5f15f7ae0e1", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3600&subgroupid=597", "backRss": [], "standbyRssList": [], "title": "末日后酒店", "offset": 0, "year": 2025, "month": 4, "date": 8, "week": 2, "season": 1, "cover": "5/572b8259134a43d2b73922f3eaa0918a.jpg", "image": "https://lain.bgm.tv/pic/cover/l/6e/1a/509986_99qsc.jpg", "subgroup": "猎户发布组", "match": ["{{猎户发布组}}:1080p", "{{猎户发布组}}:简", "{{猎户发布组}}:日", "{{猎户发布组}}:内嵌"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "mo ri hou jiu dian", "enable": true, "currentEpisodeNumber": 9, "totalEpisodeNumber": 12, "themoviedbName": "末日后酒店", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/509986", "customDownloadPath": false, "downloadPath": "/Anime/Media/末日后酒店/Season 1", "score": 8.0, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "262929", "name": "末日后酒店", "date": "2025-04-09 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441617037}, {"id": "4eea1cb0-6a93-44ce-a528-d368242c0356", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3604&subgroupid=611", "backRss": [], "standbyRssList": [], "title": "赛马娘 芦毛灰姑娘", "offset": 0, "year": 2025, "month": 4, "date": 6, "week": 0, "season": 1, "cover": "7/7d6bb9491e13e4259f93bb62fd584c80.jpg", "image": "https://lain.bgm.tv/pic/cover/l/9e/fa/509297_Cnz9B.jpg", "subgroup": "北宇治字幕组", "match": ["{{北宇治字幕组}}:繁", "{{北宇治字幕组}}:简", "{{北宇治字幕组}}:日", "{{北宇治字幕组}}:HEVC", "{{北宇治字幕组}}:内封"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "sai ma niang   lu mao hui gu niang", "enable": true, "currentEpisodeNumber": 9, "totalEpisodeNumber": 13, "themoviedbName": "赛马娘 芦毛灰姑娘", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/509297", "customDownloadPath": false, "downloadPath": "/Anime/Media/赛马娘 芦毛灰姑娘/Season 1", "score": 7.9, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "262700", "name": "赛马娘 芦毛灰姑娘", "date": "2025-04-06 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749714797008}, {"id": "35d06a43-794c-4a57-8921-4fc847374794", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3623&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "时光流逝，饭菜依旧美味", "offset": 0, "year": 2025, "month": 4, "date": 12, "week": 6, "season": 1, "cover": "6/60bbd57a8b886d7e4d4b7b8870a3234b.jpg", "image": "https://lain.bgm.tv/pic/cover/l/d3/5d/531159_BayD9.jpg", "subgroup": "喵萌奶茶屋", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "shi guang liu shi ， fan cai yi jiu mei wei", "enable": true, "currentEpisodeNumber": 9, "totalEpisodeNumber": 12, "themoviedbName": "时光流逝，饭菜依旧美味", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/531159", "customDownloadPath": false, "downloadPath": "/Anime/Media/时光流逝，饭菜依旧美味/Season 1", "score": 7.8, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "281042", "name": "时光流逝，饭菜依旧美味", "date": "2025-04-13 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749662208457}, {"id": "a4eaa0bd-b50e-40c7-8a2b-dc4ece10c343", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3628&subgroupid=576", "backRss": [], "standbyRssList": [], "title": "mono女孩", "offset": 0, "year": 2025, "month": 4, "date": 12, "week": 6, "season": 1, "cover": "5/5fd4eec4f8ac4f94219dd021d082cf3a.jpg", "image": "https://lain.bgm.tv/pic/cover/l/a6/39/485936_y0bX8.jpg", "subgroup": "MingYSub", "match": ["{{MingYS<PERSON>}}:1080p", "{{MingY<PERSON><PERSON>}}:繁", "{{MingY<PERSON><PERSON>}}:简", "{{<PERSON><PERSON><PERSON><PERSON>}}:日", "{{MingYSub}}:内封"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "m o n o nv hai", "enable": true, "currentEpisodeNumber": 7, "totalEpisodeNumber": 12, "themoviedbName": "mono女孩", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/485936", "customDownloadPath": false, "downloadPath": "/Anime/Media/mono女孩/Season 1", "score": 7.5, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "249854", "name": "mono女孩", "date": "2025-04-13 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502290839}, {"id": "0c12262f-2166-4d9b-8dcc-c78f127452f7", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3530&subgroupid=611", "backRss": [], "standbyRssList": [], "title": "药屋少女的呢喃", "offset": 0, "year": 2025, "month": 1, "date": 10, "week": 5, "season": 2, "cover": "f/f8274725cc24a2b745bae8bf5b539e8c.jpg", "image": "https://lain.bgm.tv/r/400/pic/cover/l/9e/b3/486347_jKVqi.jpg", "subgroup": "北宇治字幕组", "match": [], "exclude": ["720", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "yao wu shao nv de ni nan", "enable": true, "currentEpisodeNumber": 21, "totalEpisodeNumber": 24, "themoviedbName": "药屋少女的呢喃", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/486347", "customDownloadPath": false, "downloadPath": "/Anime/Media/药屋少女的呢喃/Season 2", "score": 7.7, "customEpisode": false, "customEpisodeStr": "\\d+(\\.5)?", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [25.0, 26.0, 27.0, 28.0, 29.0], "tmdb": {"id": "", "name": "", "date": "2025-03-27 18:21:35"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749664357374}, {"id": "077fc921-fc2d-469c-a029-5586c48b3764", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=227&subgroupid=161", "backRss": [], "standbyRssList": [], "title": "名侦探柯南", "offset": 0, "year": 1996, "month": 1, "date": 8, "week": 1, "season": 1, "cover": "a/ab63751efa7640f2562e5cbcf43cee0f.jpg", "image": "https://lain.bgm.tv/r/400/pic/cover/l/01/88/899_Q3F3X.jpg", "subgroup": "银色子弹字幕组", "match": ["1080P", "繁", "简", "日", "MKV"], "exclude": ["720", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ming zhen tan ke nan", "enable": true, "currentEpisodeNumber": 1164, "totalEpisodeNumber": 0, "themoviedbName": "名侦探柯南", "type": "mikan", "bgmUrl": "http://bgm.tv/subject/899", "customDownloadPath": false, "downloadPath": "/Anime/Media/名侦探柯南/Season 1", "score": 7.6, "customEpisode": false, "customEpisodeStr": "\\d+(\\.5)?", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": true, "notDownload": [1136.0, 31.0, 1137.0, 32.0, 1138.0, 33.0, 1139.0, 36.0, 737.0, 1140.0, 37.0, 38.0, 1141.0, 1142.0, 41.0, 1143.0, 42.0, 795.0, 43.0, 1144.0, 1145.0, 44.0, 45.0, 46.0, 47.0, 1146.0, 1147.0, 50.0, 51.0, 1148.0, 52.0, 1149.0, 53.0, 1150.0, 54.0, 55.0, 1151.0, 893.0, 56.0, 1152.0, 1153.0, 59.0, 1154.0, 60.0, 1155.0, 1156.0], "tmdb": {"id": "", "name": "", "date": "2025-03-27 18:21:35"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502297868}, {"id": "0bd8111a-eee8-467a-a0af-bc00d6e12f4a", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3599&subgroupid=611", "backRss": [], "standbyRssList": [], "title": "夏日口袋", "offset": 0, "year": 2025, "month": 4, "date": 7, "week": 1, "season": 1, "cover": "a/af7b39595e6f1d40abf06d32654a323f.jpg", "image": "https://lain.bgm.tv/pic/cover/l/23/ce/363957_pgptl.jpg", "subgroup": "北宇治字幕组", "match": ["{{北宇治字幕组}}:简", "{{北宇治字幕组}}:日", "{{北宇治字幕组}}:HEVC", "{{北宇治字幕组}}:内嵌"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "xia ri kou dai", "enable": true, "currentEpisodeNumber": 9, "totalEpisodeNumber": 26, "themoviedbName": "夏日口袋", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/363957", "customDownloadPath": false, "downloadPath": "/Anime/Media/夏日口袋/Season 1", "score": 7.3, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "271576", "name": "夏日口袋", "date": "2025-04-07 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502301462}, {"id": "48d07c42-1de1-41ba-a434-24f787396f90", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3587&subgroupid=203", "backRss": [], "standbyRssList": [], "title": "魔女与使魔", "offset": 0, "year": 2025, "month": 4, "date": 6, "week": 0, "season": 1, "cover": "3/33e42e603ed7192637286d78e754c65c.jpg", "image": "https://lain.bgm.tv/pic/cover/l/5e/4f/506672_K9EQT.jpg", "subgroup": "桜都字幕组", "match": ["{{桜都字幕组}}:1080p", "{{桜都字幕组}}:繁", "{{桜都字幕组}}:简", "{{桜都字幕组}}:内封"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "mo nv yu shi mo", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 26, "themoviedbName": "魔女与使魔", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/506672", "customDownloadPath": false, "downloadPath": "/Anime/Media/魔女与使魔/Season 1", "score": 7.2, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "261868", "name": "魔女与使魔", "date": "2025-04-06 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749660451508}, {"id": "bc2cc1c5-1049-4b3c-8618-e88d0b8008a9", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3585&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "机动战士高达 GQuuuuuuX", "offset": 0, "year": 2025, "month": 4, "date": 8, "week": 2, "season": 1, "cover": "a/a51be11d19c297d5068aa7a04991720b.jpg", "image": "https://lain.bgm.tv/pic/cover/l/27/84/526816_i7w6d.jpg", "subgroup": "LoliHouse", "match": ["{{<PERSON><PERSON><PERSON><PERSON>}}:1080p", "{{<PERSON><PERSON><PERSON><PERSON>}}:简", "{{<PERSON><PERSON><PERSON><PERSON>}}:日", "{{<PERSON><PERSON><PERSON>ouse}}:HEVC", "{{<PERSON><PERSON><PERSON><PERSON>}}:10bit", "{{<PERSON><PERSON><PERSON><PERSON>}}:内封"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ji dong zhan shi gao da   g q u u u u u u x", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "机动战士高达 GQuuuuuuX", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/526816", "customDownloadPath": false, "downloadPath": "/Anime/Media/机动战士高达 GQuuuuuuX/Season 1", "score": 7.0, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "278704", "name": "机动战士高达 GQuuuuuuX", "date": "2025-04-09 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749694729762}, {"id": "479659a6-40db-4e8d-9b97-abcaa4aec646", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3584&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "忍者与杀手的两人生活", "offset": 0, "year": 2025, "month": 4, "date": 10, "week": 4, "season": 1, "cover": "0/08e805d24eedf405e93682d011f0f5ab.jpg", "image": "https://lain.bgm.tv/pic/cover/l/15/a3/491569_R42i0.jpg", "subgroup": "喵萌奶茶屋", "match": ["{{喵萌奶茶屋}}:1080p", "{{喵萌奶茶屋}}:简", "{{喵萌奶茶屋}}:日"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ren zhe yu sha shou de liang ren sheng huo", "enable": true, "currentEpisodeNumber": 2, "totalEpisodeNumber": 12, "themoviedbName": "忍者与杀手的两人生活", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/491569", "customDownloadPath": false, "downloadPath": "/Anime/Media/忍者与杀手二人组的日常生活/Season 1", "score": 7.0, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "278050", "name": "忍者与杀手的两人生活", "date": "2025-04-10 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502312548}, {"id": "f9aa2be2-20aa-48cf-8476-59116a0df7e7", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3607&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "九龙大众浪漫", "offset": 0, "year": 2025, "month": 4, "date": 5, "week": 6, "season": 1, "cover": "6/6b667e18f61d6775b1191a6da56f08b8.jpg", "image": "https://lain.bgm.tv/pic/cover/l/3e/a1/518090_lCsju.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "jiu long da zhong lang man", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 13, "themoviedbName": "九龙大众浪漫", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/518090", "customDownloadPath": false, "downloadPath": "/Anime/Media/九龙大众浪漫/Season 1", "score": 6.8, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "274069", "name": "九龙大众浪漫", "date": "2025-04-05 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502316871}, {"id": "ec7f8747-1b69-41d5-82a1-466dde2fdfb8", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3606&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "摇滚乃是淑女的爱好", "jpTitle": "ロックは淑女の嗜みでして", "offset": 0, "year": 2025, "month": 4, "date": 3, "week": 4, "season": 1, "cover": "b/b394f2e5facf86043a7cdaf0418dc6fa.jpg", "image": "https://lain.bgm.tv/pic/cover/l/c4/b5/504678_iCezc.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "yao gun nai shi shu nv de ai hao", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 13, "themoviedbName": "摇滚乃是淑女的爱好", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/504678", "customDownloadPath": false, "downloadPath": "/Anime/Media/摇滚乃是淑女的爱好/Season 1", "score": 6.5, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "260523", "name": "摇滚乃是淑女的爱好", "originalName": "ロックは淑女の嗜みでして", "date": "2025-04-03 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502320402}, {"id": "f66e8efd-9cd0-4683-a424-7d7cd0106e7b", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3613&subgroupid=1207", "backRss": [], "standbyRssList": [], "title": "阳光马达棒球场", "offset": 0, "year": 2025, "month": 4, "date": 1, "week": 2, "season": 1, "cover": "e/eb087b7d076d336e689a80ed768e4800.jpg", "image": "https://lain.bgm.tv/pic/cover/l/a0/09/524123_5Pwit.jpg", "subgroup": "奇怪机翻组", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "yang guang ma da bang qiu chang", "enable": true, "currentEpisodeNumber": 7, "totalEpisodeNumber": 12, "themoviedbName": "阳光马达棒球场", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/524123", "customDownloadPath": false, "downloadPath": "/Anime/Media/阳光马达棒球场！/Season 1", "score": 6.7, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "277222", "name": "阳光马达棒球场", "date": "2025-04-02 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502323978}, {"id": "b0b0bf70-8031-4ae8-a1b1-3b05b00d0eba", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3612&subgroupid=570", "backRss": [], "standbyRssList": [], "title": "测不准的阿波连同学", "offset": 0, "year": 2025, "month": 4, "date": 7, "week": 1, "season": 2, "cover": "0/0b4654b37e74a33a96cff5bea17517a3.jpg", "image": "https://lain.bgm.tv/pic/cover/l/ed/2b/506922_8sn3u.jpg", "subgroup": "霜庭云花", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ce bu zhun de a bo lian tong xue", "enable": true, "currentEpisodeNumber": 2, "totalEpisodeNumber": 12, "themoviedbName": "测不准的阿波连同学", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/506922", "customDownloadPath": false, "downloadPath": "/Anime/Media/测不准的阿波连同学 第二季/Season 2", "score": 6.5, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "130237", "name": "测不准的阿波连同学", "date": "2022-04-02 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502327668}, {"id": "5dcf7b07-9e41-4398-adc0-18ea4f89cd46", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3627&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "搞笑漫画日和GO", "offset": 0, "year": 2025, "month": 4, "date": 7, "week": 1, "season": 1, "cover": "7/7e575dc9855e2ca06bb3e9f3b0fd765d.jpg", "image": "https://lain.bgm.tv/pic/cover/l/b6/dc/526567_5rTtj.jpg", "subgroup": "喵萌奶茶屋", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "gao xiao man hua ri he g o", "enable": true, "currentEpisodeNumber": 3, "totalEpisodeNumber": 0, "themoviedbName": "", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/526567", "customDownloadPath": false, "downloadPath": "/Anime/Media/搞笑漫画日和GO/Season 1", "score": 6.5, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "", "name": "", "date": "2025-04-06 22:39:38"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502331222}, {"id": "dde48a7c-55d6-4801-812c-fe87fc45915b", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3611&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "炎炎消防队", "jpTitle": "炎炎ノ消防隊 参ノ章", "offset": 0, "year": 2025, "month": 4, "date": 4, "week": 5, "season": 1, "cover": "c/ce550e1179a4d1cf0e8616c3ce03be6e.jpg", "image": "https://lain.bgm.tv/pic/cover/l/24/61/382126_tsZds.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "yan yan xiao fang dui", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "炎炎消防队", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/382126", "customDownloadPath": false, "downloadPath": "/Anime/Media/炎炎消防队 叁之章/Season 1", "score": 6.3, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "88046", "name": "炎炎消防队", "originalName": "炎炎ノ消防隊", "date": "2019-07-05 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502334881}, {"id": "3e2560ba-0143-43f4-8d36-a1b8fbe1ac93", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3598&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "随兴旅-That's Journey-", "offset": 0, "year": 2025, "month": 4, "date": 7, "week": 1, "season": 1, "cover": "f/f96aec6d512787bc584ca74496f9f906.jpg", "image": "https://lain.bgm.tv/pic/cover/l/e0/10/437593_jrDd8.jpg", "subgroup": "喵萌奶茶屋", "match": ["{{喵萌奶茶屋}}:1080p", "{{喵萌奶茶屋}}:简", "{{喵萌奶茶屋}}:日"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "sui xing lv - t h a t ' s   j o u r n e y -", "enable": true, "currentEpisodeNumber": 9, "totalEpisodeNumber": 12, "themoviedbName": "随兴旅-That's Journey-", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/437593", "customDownloadPath": false, "downloadPath": "/Anime/Media/随兴旅-That's Journey-/Season 1", "score": 6.3, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "254853", "name": "随兴旅-That's Journey-", "date": "2025-04-07 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502338491}, {"id": "3a3e3ff4-4211-4c61-8c27-07de140bcf6a", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3614&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "紫云寺家的兄弟姐妹", "offset": 0, "year": 2025, "month": 4, "date": 8, "week": 2, "season": 1, "cover": "9/9d9a51dbc9e7242b405650d2f4e66e49.jpg", "image": "https://lain.bgm.tv/pic/cover/l/48/c8/480545_gGwa0.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "zi yun si jia de xiong di jie mei", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/480545", "customDownloadPath": false, "downloadPath": "/Anime/Media/紫云寺家的兄弟姐妹/Season 1", "score": 6.1, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "", "name": "", "date": "2025-04-09 23:28:38"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749615987550}, {"id": "51d47db4-feb2-48ec-aa64-03e399ee92db", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3642&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "每日男公关", "offset": 0, "year": 2025, "month": 4, "date": 4, "week": 5, "season": 1, "cover": "6/6a29f07b82b390d1ccbd062a7d9f0dd6.jpg", "image": "https://lain.bgm.tv/pic/cover/l/4f/81/529078_R4K76.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "mei ri nan gong guan", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 0, "themoviedbName": "每日男公关", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/529078", "customDownloadPath": false, "downloadPath": "/Anime/Media/每日男公关/Season 1", "score": 6.0, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "279710", "name": "每日男公关", "date": "2025-04-05 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502346572}, {"id": "40ec3104-c88f-472d-b22f-35896499fe71", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3624&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "拉撒路", "offset": 0, "year": 2025, "month": 4, "date": 6, "week": 0, "season": 1, "cover": "c/c40c8791e542e96118e174c0b73550b6.jpg", "image": "https://lain.bgm.tv/pic/cover/l/29/22/446296_rRM5n.jpg", "subgroup": "喵萌奶茶屋", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "la sa lu", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 13, "themoviedbName": "拉撒路", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/446296", "customDownloadPath": false, "downloadPath": "/Anime/Media/拉撒路/Season 1", "score": 5.8, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "231003", "name": "拉撒路", "date": "2025-04-06 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749556630704}, {"id": "a8110396-3b90-4efb-b37b-6be3cfbdbed4", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3583&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "直至魔女消逝", "offset": 0, "year": 2025, "month": 4, "date": 1, "week": 2, "season": 1, "cover": "c/c4604edb78009f8ff7b0ff59e15bbc14.jpg", "image": "https://lain.bgm.tv/pic/cover/l/e1/b7/501702_VXrd2.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "zhi zhi mo nv xiao shi", "enable": true, "currentEpisodeNumber": 11, "totalEpisodeNumber": 12, "themoviedbName": "直到魔女消逝", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/501702", "customDownloadPath": false, "downloadPath": "/Anime/Media/直至魔女消逝/Season 1", "score": 5.9, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "258055", "name": "直到魔女消逝", "date": "2025-04-01 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749732721078}, {"id": "bc46cb82-ede9-46ed-a888-21e430195d14", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3594&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "外星人姆姆", "offset": 0, "year": 2025, "month": 4, "date": 9, "week": 3, "season": 1, "cover": "7/7cbea729c585cb04cf730a19bb132021.jpg", "image": "https://lain.bgm.tv/pic/cover/l/1f/8e/526043_BzVh4.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "wai xing ren mu mu", "enable": true, "currentEpisodeNumber": 1, "totalEpisodeNumber": 0, "themoviedbName": "外星人姆姆", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/526043", "customDownloadPath": false, "downloadPath": "/Anime/Media/外星人姆姆/Season 1", "score": 5.8, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "278367", "name": "外星人姆姆", "date": "2025-04-10 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502357490}, {"id": "2258be2e-de18-46d5-a508-ac0575fddbc1", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3609&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "圣女因太过完美不够可爱而被废除婚约并卖到邻国", "offset": 0, "year": 2025, "month": 4, "date": 9, "week": 3, "season": 1, "cover": "5/52e970dee14fb5626111cb9631fc7b3d.jpg", "image": "https://lain.bgm.tv/pic/cover/l/a3/33/520672_9DC90.jpg", "subgroup": "喵萌奶茶屋", "match": ["{{喵萌奶茶屋}}:1080p", "{{喵萌奶茶屋}}:简", "{{喵萌奶茶屋}}:日"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "sheng nv yin tai guo wan mei bu gou ke ai er bei fei chu hun yue bing mai dao lin guo", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "圣女因太过完美不够可爱而被废除婚约并卖到邻国", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/520672", "customDownloadPath": false, "downloadPath": "/Anime/Media/圣女因太过完美不够可爱而被废除婚约并卖到邻国/Season 1", "score": 5.7, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "274741", "name": "圣女因太过完美不够可爱而被废除婚约并卖到邻国", "date": "2025-04-10 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502361031}, {"id": "1d2eae4c-9741-4aaf-ae96-a8ca90a0e8b6", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3596&subgroupid=1230", "backRss": [], "standbyRssList": [], "title": "公主专场管弦乐", "offset": 0, "year": 2025, "month": 4, "date": 6, "week": 0, "season": 1, "cover": "f/f97039332a183a40520ceaf41a12d76c.jpg", "image": "https://lain.bgm.tv/pic/cover/l/4c/f7/499169_Lq2RK.jpg", "subgroup": "Prejudice-Studio", "match": ["{{Prejudice-Studio}}:1080p", "{{Prejudice-Studio}}:繁", "{{Prejudice-Studio}}:简", "{{Prejudice-Studio}}:日", "{{Prejudice-Studio}}:内封"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "gong zhu zhuan chang guan xian le", "enable": true, "currentEpisodeNumber": 4, "totalEpisodeNumber": 0, "themoviedbName": "公主专场管弦乐", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/499169", "customDownloadPath": false, "downloadPath": "/Anime/Media/公主的管弦乐团/Season 1", "score": 5.7, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "256853", "name": "公主专场管弦乐", "date": "2025-04-06 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502365097}, {"id": "8f3f1550-249a-43db-920e-6f478a02b79e", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3580&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "鬼人幻灯抄", "offset": 0, "year": 2025, "month": 3, "date": 31, "week": 1, "season": 1, "cover": "2/27022b749db2dca7eb056371755db93e.jpg", "image": "https://lain.bgm.tv/pic/cover/l/b8/b8/404753_v9Wri.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "gui ren huan deng chao", "enable": true, "currentEpisodeNumber": 11, "totalEpisodeNumber": 0, "themoviedbName": "鬼人幻灯抄", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/404753", "customDownloadPath": false, "downloadPath": "/Anime/Media/鬼人幻灯抄/Season 1", "score": 5.9, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "133154", "name": "鬼人幻灯抄", "date": "2025-03-31 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749548261183}, {"id": "43c907c1-b100-424e-b79d-40535213c9d3", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3591&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "乡下大叔成为剑圣", "offset": 0, "year": 2025, "month": 4, "date": 5, "week": 6, "season": 1, "cover": "a/a0d819c3f866065d0cdb2124cb69d2d0.jpg", "image": "https://lain.bgm.tv/pic/cover/l/5f/43/506258_4m4PH.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "xiang xia da shu cheng wei jian sheng", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "乡下大叔成为剑圣", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/506258", "customDownloadPath": false, "downloadPath": "/Anime/Media/乡下大叔成为剑圣/Season 1", "score": 5.7, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "260823", "name": "乡下大叔成为剑圣", "date": "2025-04-05 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502373293}, {"id": "4791e5cc-9fbb-41b7-9a5a-d6193578cdde", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3621&subgroupid=202", "backRss": [], "standbyRssList": [], "title": "快藏起来！玛琪娜同学！！", "jpTitle": "かくして! マキナさん!!", "offset": 0, "year": 2025, "month": 4, "date": 6, "week": 0, "season": 1, "cover": "4/4d40ab71f7cb234c0d83e122e16a9b34.jpg", "image": "https://lain.bgm.tv/pic/cover/l/a5/6a/493813_952oq.jpg", "subgroup": "生肉 or 不明字幕", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "kuai zang qi lai ！ ma qi na tong xue ！ ！", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "快藏起来！玛琪娜同学！！", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/493813", "customDownloadPath": false, "downloadPath": "/Anime/Media/快藏起来！玛琪娜同学!!/Season 1", "score": 5.7, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": true, "notDownload": [1.0, 2.0, 3.0, 4.0], "tmdb": {"id": "254174", "name": "快藏起来！玛琪娜同学！！", "originalName": "かくして！ マキナさん!!", "date": "2025-04-07 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749502377066}, {"id": "24cf9b17-f2f4-4c54-adbc-20648f90c71e", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3522&subgroupid=1210", "backRss": [], "standbyRssList": [], "title": "魔法制造者 ～异世界魔法的制作方法～", "offset": 0, "year": 2025, "month": 1, "date": 8, "week": 3, "season": 1, "cover": "6/6d8fa04fef905b5a6d248267cea21db2.jpg", "image": "https://lain.bgm.tv/r/400/pic/cover/l/00/da/503268_SdLDD.jpg", "subgroup": "黑白字幕组", "match": [], "exclude": ["720", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "mo fa zhi zao zhe   ～ yi shi jie mo fa de zhi zuo fang fa ～", "enable": true, "currentEpisodeNumber": 11, "totalEpisodeNumber": 12, "themoviedbName": "魔法制造者 ～异世界魔法的制作方法～", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/503268", "customDownloadPath": false, "downloadPath": "/Anime/Media/魔法制造者 ～异世界魔法的制作方法～/Season 1", "score": 5.7, "customEpisode": false, "customEpisodeStr": "\\d+(\\.5)?", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": true, "notDownload": [], "tmdb": {"id": "", "name": "", "date": "2025-03-27 18:21:35"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441700106}, {"id": "c5156ca3-3498-4aa8-844c-50c30d85c88a", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3634&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "防风少年 第二季", "offset": 0, "year": 2025, "month": 4, "date": 3, "week": 4, "season": 1, "cover": "b/b2ee5ffcdc886e5c8a6a9edd38f35766.jpg", "image": "https://lain.bgm.tv/pic/cover/l/50/38/501626_7DbAO.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "fang feng shao nian   di er ji", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "防风少年", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/501626", "customDownloadPath": false, "downloadPath": "/Anime/Media/防风少年 第二季/Season 1", "score": 5.6, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "223500", "name": "防风少年", "date": "2024-04-05 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441702687}, {"id": "9f312717-8c22-47a7-add2-682dca044153", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3602&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "男女之间的友情存在吗？（不，不存在!!）", "offset": 0, "year": 2025, "month": 4, "date": 4, "week": 5, "season": 1, "cover": "e/ec7c4e94b3ab67bdfd2198f952d0de81.jpg", "image": "https://lain.bgm.tv/pic/cover/l/17/74/395493_FAWf3.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇", "1080[Pp]"], "globalExclude": false, "ova": false, "pinyin": "nan nv zhi jian de you qing cun zai ma ？ （ bu ， bu cun zai ! ! ）", "enable": true, "currentEpisodeNumber": 0, "totalEpisodeNumber": 12, "themoviedbName": "男女之间存在纯友情吗？（不，不存在！！）", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/395493", "customDownloadPath": false, "downloadPath": "/Anime/Media/男女之间的友情存在吗？（不，不存在!!）/Season 1", "score": 5.4, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "217407", "name": "男女之间存在纯友情吗？（不，不存在！！）", "originalName": "男女の友情は成立する？（いや、しないっ!!）", "date": "2025-04-04 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441706162}, {"id": "86db0342-c675-4483-8006-e466f0984d26", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3556&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "脱离了A级队伍的我，和从前的徒弟们前往迷宫深处。", "offset": 0, "year": 2025, "month": 1, "date": 11, "week": 6, "season": 1, "cover": "2/274af5115315dc3961c344790d8f5cc4.jpg", "image": "https://lain.bgm.tv/r/400/pic/cover/l/55/98/511126_w342E.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "tuo li le a ji dui wu de wo ， he cong qian de tu di men qian wang mi gong shen chu 。", "enable": true, "currentEpisodeNumber": 21, "totalEpisodeNumber": 24, "themoviedbName": "离开A级队伍的我，和从前的弟子往迷宫深处迈进", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/511126", "customDownloadPath": false, "downloadPath": "/Anime/Media/脱离了A级队伍的我，和从前的徒弟们前往迷宫深处。/Season 1", "score": 5.1, "customEpisode": false, "customEpisodeStr": "\\d+(\\.5)?", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": true, "notDownload": [], "tmdb": {"id": "", "name": "", "date": "2025-03-27 18:21:35"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441709132}, {"id": "204e492e-98d0-4b5f-af4b-0956b9db4a74", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3620&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "记忆缝线", "offset": 0, "year": 2025, "month": 4, "date": 2, "week": 3, "season": 1, "cover": "e/e06063f333639e312d65319c74e30a28.jpg", "image": "https://lain.bgm.tv/pic/cover/l/14/39/445826_id8dk.jpg", "subgroup": "喵萌奶茶屋", "match": ["{{喵萌奶茶屋}}:1080p", "{{喵萌奶茶屋}}:简", "{{喵萌奶茶屋}}:日"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ji yi feng xian", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 0, "themoviedbName": "记忆缝线", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/445826", "customDownloadPath": false, "downloadPath": "/Anime/Media/记忆缝线/Season 1", "score": 5.0, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "279731", "name": "记忆缝线", "date": "2025-04-02 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441712064}, {"id": "7f6e9eff-0763-4d98-b808-9dd0334c7012", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3593&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "天才治疗师退队作为无照治疗师快乐过活", "jpTitle": "一瞬で治療していたのに役立たずと追放された天才治癒師、闇ヒーラーとして楽しく生きる", "offset": 0, "year": 2025, "month": 4, "date": 3, "week": 4, "season": 1, "cover": "cover.png", "image": "https://lain.bgm.tv/pic/cover/l/20/a4/484402_S8jgS.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "tian cai zhi liao shi tui dui zuo wei wu zhao zhi liao shi kuai le guo huo", "enable": true, "currentEpisodeNumber": 11, "totalEpisodeNumber": 12, "themoviedbName": "天才治疗师退队作为无照治疗师快乐过活", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/484402", "customDownloadPath": false, "downloadPath": "/Anime/Media/瞬间治疗却被视为无用而被流放的天才治疗师，以暗黑治疗师的身份幸福地生活着/Season 1", "score": 4.9, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": true, "notDownload": [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0], "tmdb": {"id": "276253", "name": "天才治疗师退队作为无照治疗师快乐过活", "originalName": "一瞬で治療していたのに役立たずと追放された天才治癒師、闇ヒーラーとして楽しく生きる", "date": "2025-04-03 00:00:00", "tmdbGroupId": ""}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749756326479}, {"id": "da23cf0b-df1a-485c-896f-a95c9ba5477c", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3619&subgroupid=597", "backRss": [], "standbyRssList": [], "title": "正义使者 - 我的英雄学院之非法英雄", "offset": 0, "year": 2025, "month": 4, "date": 7, "week": 1, "season": 1, "cover": "8/88f6c19d45c6c944ed3a218f49554461.jpg", "image": "https://lain.bgm.tv/pic/cover/l/ac/20/529995_aR8M0.jpg", "subgroup": "猎户发布组", "match": ["{{猎户发布组}}:1080p", "{{猎户发布组}}:简", "{{猎户发布组}}:日", "{{猎户发布组}}:内嵌"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "zheng yi shi zhe   -   wo de ying xiong xue yuan zhi fei fa ying xiong", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 13, "themoviedbName": "正义使者 - 我的英雄学院之非法英雄", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/529995", "customDownloadPath": false, "downloadPath": "/Anime/Media/正义使者 -我的英雄学院之非法英雄-/Season 1", "score": 4.8, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "280110", "name": "正义使者 - 我的英雄学院之非法英雄", "date": "2025-04-07 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749740918383}, {"id": "27f69d87-25ff-4d47-942e-6ddb874c9a72", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3592&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "我是星际国家的恶德领主！", "offset": 0, "year": 2025, "month": 4, "date": 5, "week": 6, "season": 1, "cover": "b/be3051bd70bce3db12201411342ef69d.jpg", "image": "https://lain.bgm.tv/pic/cover/l/26/c8/520645_4Z737.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "wo shi xing ji guo jia de e de ling zhu ！", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 0, "themoviedbName": "我是星际国家的恶德领主！", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/520645", "customDownloadPath": false, "downloadPath": "/Anime/Media/我是星际国家的恶德领主！/Season 1", "score": 4.6, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "274737", "name": "我是星际国家的恶德领主！", "date": "2025-04-06 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441721509}, {"id": "93df5592-def2-4f6b-bd44-e7799824aa99", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3617&subgroupid=370", "backRss": [], "standbyRssList": [], "title": "爱有点沉重的暗黑精灵从异世界紧追不放", "offset": 0, "year": 2025, "month": 4, "date": 6, "week": 0, "season": 1, "cover": "0/0d7b4f12e397236fc348872f1f732fe3.jpg", "image": "https://lain.bgm.tv/pic/cover/l/b9/9d/511264_0rAtm.jpg", "subgroup": "LoliHouse", "match": [], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ai you dian chen zhong de an hei jing ling cong yi shi jie jin zhui bu fang", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "爱有点沉重的暗黑精灵从异世界紧追不放", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/511264", "customDownloadPath": false, "downloadPath": "/Anime/Media/爱有些沉重的黑暗精灵从异世界追过来了/Season 1", "score": 4.5, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "271003", "name": "爱有点沉重的暗黑精灵从异世界紧追不放", "date": "2025-04-07 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441724119}, {"id": "439dd3d7-cb76-41f3-b966-ca7d0f637417", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3603&subgroupid=203", "backRss": [], "standbyRssList": [], "title": "请穿上内衣吧，鹰峰同学", "jpTitle": "履いてください、鷹峰さん", "offset": 0, "year": 2025, "month": 4, "date": 2, "week": 3, "season": 1, "cover": "c/c5213bca642269af67b07be508923388.jpg", "image": "https://lain.bgm.tv/pic/cover/l/05/c1/506407_8l3G3.jpg", "subgroup": "桜都字幕组", "match": ["{{桜都字幕组}}:1080p", "{{桜都字幕组}}:繁", "{{桜都字幕组}}:简", "{{桜都字幕组}}:日", "{{桜都字幕组}}:内封"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "qing chuan shang nei yi ba ， ying feng tong xue", "enable": true, "currentEpisodeNumber": 10, "totalEpisodeNumber": 12, "themoviedbName": "拜托请穿上，鹰峰同学", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/506407", "customDownloadPath": false, "downloadPath": "/Anime/Media/拜托请穿上，鹰峰同学/Season 1", "score": 4.4, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "276880", "name": "拜托请穿上，鹰峰同学", "originalName": "履いてください、鷹峰さん", "date": "2025-04-02 00:00:00", "tmdbGroupId": ""}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441727057}, {"id": "03a1d249-1620-4a6b-beaa-78aaaf7c6dee", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3548&subgroupid=1210", "backRss": [], "standbyRssList": [], "title": "我的可爱对黑岩目高不管用", "offset": 0, "year": 2025, "month": 1, "date": 6, "week": 1, "season": 1, "cover": "9/9f0f983ee7e7b282b76535141edb3149.jpg", "image": "https://lain.bgm.tv/r/400/pic/cover/l/fc/7d/494267_upx1c.jpg", "subgroup": "黑白字幕组", "match": [], "exclude": ["720", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "wo de ke ai dui hei yan mu gao bu guan yong", "enable": true, "currentEpisodeNumber": 9, "totalEpisodeNumber": 12, "themoviedbName": "我的可爱对黑岩目高不管用", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/494267", "customDownloadPath": false, "downloadPath": "/Anime/Media/我的可爱对黑岩目高不管用/Season 1", "score": 4.4, "customEpisode": false, "customEpisodeStr": "\\d+(\\.5)?", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": true, "notDownload": [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0], "tmdb": {"id": "", "name": "", "date": "2025-03-27 18:21:35"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441730632}, {"id": "ad02a18b-16bf-46dc-a0a9-1e128457cb9d", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3383&subgroupid=382", "backRss": [], "standbyRssList": [], "title": "你与我最后的战场，亦或是世界起始的圣战", "offset": 0, "year": 2024, "month": 7, "date": 10, "week": 3, "season": 2, "cover": "8/809ff53612d7efab768ea4a564597eb7.jpg", "image": "https://lain.bgm.tv/pic/cover/l/e5/fc/351349_Z8mpG.jpg", "subgroup": "喵萌奶茶屋", "match": ["{{喵萌奶茶屋}}:1080p", "{{喵萌奶茶屋}}:简", "{{喵萌奶茶屋}}:日"], "exclude": ["720[Pp]", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "ni yu wo zui hou de zhan chang ， yi huo shi shi jie qi shi de sheng zhan", "enable": true, "currentEpisodeNumber": 1, "totalEpisodeNumber": 12, "themoviedbName": "你与我最后的战场，亦或是世界起始的圣战", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/351349", "customDownloadPath": false, "downloadPath": "/Anime/Media/你与我最后的战场，亦或是世界起始的圣战 第二季/Season 2", "score": 4.4, "customEpisode": false, "customEpisodeStr": "( - |Vol |[Ee][Pp]?)\\d+(\\.5)?|【\\d+(\\.5)?】|\\[\\d+(\\.5)?( ?[vV]\\d)?( ?END)?( ?完)?]|第?\\d+(\\.5)?[话話集]( - END)?|^\\[TOC].* \\d+", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [], "tmdb": {"id": "101972", "name": "你与我最后的战场，亦或是世界起始的圣战", "date": "2020-10-04 00:00:00"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441733294}, {"id": "8e09ca1e-e27c-45fd-b186-a2d49c68fcc4", "url": "https://mikanime.tv/RSS/Bangumi?bangumiId=3565&subgroupid=426", "backRss": [], "standbyRssList": [], "title": "暗芝居", "offset": 0, "year": 2025, "month": 1, "date": 5, "week": 0, "season": 14, "cover": "e/e357ff69817da7092d594d0c635bc2ea.jpg", "image": "https://lain.bgm.tv/r/400/pic/cover/l/99/e5/526223_mLhZG.jpg", "subgroup": "悠哈C9字幕社", "match": ["1080p", "CHS"], "exclude": ["720", "\\d-\\d", "合集", "特别篇"], "globalExclude": false, "ova": false, "pinyin": "an zhi ju", "enable": true, "currentEpisodeNumber": 13, "totalEpisodeNumber": 0, "themoviedbName": "暗芝居", "type": "mikan", "bgmUrl": "https://bgm.tv/subject/526223", "customDownloadPath": false, "downloadPath": "/Anime/Media/暗芝居/Season 14", "score": 4.1, "customEpisode": false, "customEpisodeStr": "\\d+(\\.5)?", "customEpisodeGroupIndex": 0, "omit": true, "downloadNew": false, "notDownload": [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0], "tmdb": {"id": "", "name": "", "date": "2025-03-27 18:21:35"}, "upload": true, "procrastinating": true, "customRenameTemplateEnable": false, "customRenameTemplate": "${title} S${seasonFormat}E${episodeFormat}", "lastDownloadTime": 1749441736229}]