#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单文件夹重命名功能
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_folder_rename():
    """测试简单文件夹重命名功能"""
    print("开始测试简单文件夹重命名功能...")
    
    # 创建临时测试目录
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"创建临时测试目录: {temp_dir}")
        
        # 创建测试文件夹结构
        test_folders = [
            "战栗空间.Panic.Room.2002.USA.UHD.Bluray.2160p.REMUX.DoVi.HDR10.HEVC.Atmos.TrueHD.7.1-UBits",
            "护航.The.Arctic.Convoy.2023.2160p.GER.UHD.SDR.Blu-ray.REMUX.HEVC.DTS-HD.MA.5.1-UBits",
            "插翅难飞.Flight.Risk.2025.UHD.BluRay.2160p.REMUX.HDR10.HEVC.Atmos.TrueHD 7.1-UBits"
        ]
        
        created_folders = []
        for folder_name in test_folders:
            folder_path = os.path.join(temp_dir, folder_name)
            os.makedirs(folder_path, exist_ok=True)
            
            # 在每个文件夹中创建一个测试视频文件
            test_file = os.path.join(folder_path, f"{folder_name}.mkv")
            with open(test_file, 'w') as f:
                f.write("test video file")
            
            created_folders.append(folder_path)
            print(f"创建测试文件夹: {folder_path}")
        
        # 导入重命名模块
        try:
            import mp重命名v2
            print("成功导入mp重命名v2模块")
        except ImportError as e:
            print(f"导入模块失败: {e}")
            return False
        
        # 创建模拟的重命名处理器
        class MockRenameProcessor:
            def __init__(self):
                self.file_paths = []
                self.folder_rename_format = "{title} ({year})"
                self.dest_mode = 'same'
                self.custom_dest_path = ""
                self.preview_only = True
                self.custom_tmdbid_enabled = False
                self.custom_tmdbid_value = ""
                self.tmdb_api_key = ""
                self.custom_tmdb_media_type = "movie"
                
                # 创建缓存管理器
                self.cache_manager = mp重命名v2.CacheManager()
                
            def log_signal_emit(self, message):
                print(f"[LOG] {message}")
                
            def preview_signal_emit(self, old_path, new_path, file_count, mode):
                print(f"[PREVIEW] {old_path} -> {new_path} ({file_count} files, {mode})")
        
        # 测试路径验证功能
        processor = MockRenameProcessor()
        processor.log_signal = Mock()
        processor.log_signal.emit = processor.log_signal_emit
        processor.preview_signal = Mock()
        processor.preview_signal.emit = processor.preview_signal_emit
        
        # 绑定方法
        processor.clean_file_path = mp重命名v2.VideoRenameProcessor.clean_file_path.__get__(processor, MockRenameProcessor)
        processor._normalize_file_path = mp重命名v2.VideoRenameProcessor._normalize_file_path.__get__(processor, MockRenameProcessor)
        processor._validate_path = mp重命名v2.VideoRenameProcessor._validate_path.__get__(processor, MockRenameProcessor)
        processor.simple_rename_folder = mp重命名v2.VideoRenameProcessor.simple_rename_folder.__get__(processor, MockRenameProcessor)
        
        # 绑定新的方法
        processor._extract_basic_info_from_folder_name = mp重命名v2.VideoRenameProcessor._extract_basic_info_from_folder_name.__get__(processor, MockRenameProcessor)
        processor._clean_title_from_folder_name = mp重命名v2.VideoRenameProcessor._clean_title_from_folder_name.__get__(processor, MockRenameProcessor)

        # 测试基本信息提取
        print("\n测试基本信息提取...")
        test_folder_names = [
            "战栗空间.Panic.Room.2002.USA.UHD.Bluray.2160p.REMUX.DoVi.HDR10.HEVC.Atmos.TrueHD.7.1-UBits",
            "The.Curious.Journey.Of.Chen.Er-Gou.S01.EP01-17.2016.1080p.WEB-DL.x264.AAC-HQC",
            "护航.The.Arctic.Convoy.2023.2160p.GER.UHD.SDR.Blu-ray.REMUX.HEVC.DTS-HD.MA.5.1-UBits"
        ]

        for folder_name in test_folder_names:
            print(f"\n测试文件夹名: {folder_name}")
            title, year, media_type = processor._extract_basic_info_from_folder_name(folder_name)
            print(f"  提取结果: 标题='{title}', 年份='{year}', 类型='{media_type}'")

        # 测试路径清理和验证
        print("\n测试路径清理和验证...")
        for folder_path in created_folders:
            print(f"\n测试文件夹: {folder_path}")

            # 测试文件夹路径验证
            clean_path = processor.clean_file_path(folder_path)
            if clean_path:
                print(f"✓ 文件夹路径验证成功: {clean_path}")

                # 测试简单重命名功能
                print("测试简单重命名功能...")
                result = processor.simple_rename_folder(clean_path)
                if result is not None:
                    print(f"✓ 简单重命名测试成功")
                else:
                    print(f"✗ 简单重命名测试失败")
            else:
                print(f"✗ 文件夹路径验证失败")
        
        print("\n测试完成!")
        return True

if __name__ == "__main__":
    try:
        success = test_simple_folder_rename()
        if success:
            print("所有测试通过!")
            sys.exit(0)
        else:
            print("测试失败!")
            sys.exit(1)
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
