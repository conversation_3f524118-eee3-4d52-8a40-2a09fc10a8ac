#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TMDB ID格式修复的脚本 - 支持多种格式
"""
import re

def test_tmdbid_replacement():
    """测试TMDB ID替换逻辑 - 完整兼容性版本"""

    def new_logic(folder_rename_format, tmdbid_for_naming):
        """新的兼容性逻辑，支持多种TMDB ID格式"""
        new_folder_name = folder_rename_format.replace("{title}", "科洛弗道10号").replace("{year}", "2016")

        # 支持多种TMDB ID标签格式的兼容性处理
        if tmdbid_for_naming != "N/A":
            # 支持的格式列表（按优先级排序）
            tmdb_formats = [
                # 花括号格式
                ("{tmdbid=" + tmdbid_for_naming + "}", "{tmdbid=xxxx}"),
                ("{tmdbid-" + tmdbid_for_naming + "}", "{tmdbid-xxxx}"),
                ("{tmdb-" + tmdbid_for_naming + "}", "{tmdb-xxxx}"),
                ("{tmdb=" + tmdbid_for_naming + "}", "{tmdb=xxxx}"),
                # 方括号格式
                ("[tmdbid=" + tmdbid_for_naming + "]", "[tmdbid=xxxx]"),
                ("[tmdbid-" + tmdbid_for_naming + "]", "[tmdbid-xxxx]"),
                ("[tmdb=" + tmdbid_for_naming + "]", "[tmdb=xxxx]"),
                ("[tmdb-" + tmdbid_for_naming + "]", "[tmdb-xxxx]"),
                # 占位符格式（需要特殊处理）
                (tmdbid_for_naming, "{tmdbid}"),
            ]

            # 逐一替换支持的格式
            for replacement, pattern in tmdb_formats:
                if pattern in new_folder_name:
                    new_folder_name = new_folder_name.replace(pattern, replacement)
                    print(f"    [DEBUG] TMDB格式替换: '{pattern}' -> '{replacement}'")
                    break

            # 处理嵌套占位符格式（如 {tmdbid-{tmdbid}}）
            nested_patterns = [
                (r'\{tmdbid-\{tmdbid\}\}', f'{{tmdbid-{tmdbid_for_naming}}}'),
                (r'\{tmdb-\{tmdbid\}\}', f'{{tmdb-{tmdbid_for_naming}}}'),
                (r'\{tmdbid=\{tmdbid\}\}', f'{{tmdbid={tmdbid_for_naming}}}'),
                (r'\{tmdb=\{tmdbid\}\}', f'{{tmdb={tmdbid_for_naming}}}'),
            ]

            for pattern, replacement in nested_patterns:
                if re.search(pattern, new_folder_name):
                    new_folder_name = re.sub(pattern, replacement, new_folder_name)
                    print(f"    [DEBUG] 嵌套TMDB格式替换: '{pattern}' -> '{replacement}'")
                    break
        else:
            # 当没有TMDB ID时，清理所有可能的TMDB格式占位符
            cleanup_patterns = [
                # 花括号格式
                r'\{tmdbid=xxxx\}', r'\{tmdbid-xxxx\}', r'\{tmdb-xxxx\}', r'\{tmdb=xxxx\}',
                r'\{tmdbid=\{tmdbid\}\}', r'\{tmdbid-\{tmdbid\}\}', r'\{tmdb-\{tmdbid\}\}', r'\{tmdb=\{tmdbid\}\}',
                r'\{tmdbid\}',
                # 方括号格式
                r'\[tmdbid=xxxx\]', r'\[tmdbid-xxxx\]', r'\[tmdb-xxxx\]', r'\[tmdb=xxxx\]',
                r'\[tmdbid=\{tmdbid\}\]', r'\[tmdbid-\{tmdbid\}\]', r'\[tmdb-\{tmdbid\}\]', r'\[tmdb=\{tmdbid\}\]',
            ]

            for pattern in cleanup_patterns:
                new_folder_name = re.sub(pattern, '', new_folder_name)

            new_folder_name = new_folder_name.strip()
            print(f"    [DEBUG] 清理TMDB占位符后: '{new_folder_name}'")

        return new_folder_name

    # 全面的测试用例 - 涵盖图片中显示的所有格式
    test_cases = [
        # 你想要的格式
        ("{title} ({year}) {tmdb-xxxx}", "333371", "你的首选格式"),

        # 花括号格式
        ("{title} ({year}) {tmdbid=xxxx}", "333371", "花括号等号格式"),
        ("{title} ({year}) {tmdbid-xxxx}", "333371", "花括号短横线格式"),
        ("{title} ({year}) {tmdb=xxxx}", "333371", "短名等号格式"),
        ("{title} ({year}) {tmdbid}", "333371", "简单占位符格式"),

        # 方括号格式（推荐）
        ("{title} ({year}) [tmdbid=xxxx]", "333371", "方括号等号格式（推荐）"),
        ("{title} ({year}) [tmdbid-xxxx]", "333371", "方括号短横线格式"),
        ("{title} ({year}) [tmdb=xxxx]", "333371", "方括号短名等号格式"),
        ("{title} ({year}) [tmdb-xxxx]", "333371", "方括号短名短横线格式"),

        # 嵌套占位符格式
        ("{title} ({year}) {tmdbid-{tmdbid}}", "333371", "嵌套占位符格式"),
        ("{title} ({year}) {tmdb-{tmdbid}}", "333371", "嵌套短名格式"),

        # 无TMDB ID的情况
        ("{title} ({year}) {tmdb-xxxx}", "N/A", "无TMDB ID - 清理格式"),
        ("{title} ({year}) [tmdbid=xxxx]", "N/A", "无TMDB ID - 清理方括号"),
    ]

    print("🎬 TMDB ID 多格式兼容性测试")
    print("=" * 80)

    for i, (format_str, tmdbid, description) in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {description}")
        print(f"   格式模板: {format_str}")
        print(f"   TMDB ID: {tmdbid}")

        result = new_logic(format_str, tmdbid)

        print(f"   ✅ 最终结果: {result}")

        # 检查是否还有未替换的占位符
        has_placeholders = any(placeholder in result for placeholder in ['{tmdb', '[tmdb', 'xxxx'])
        success = not has_placeholders if tmdbid != "N/A" else not any(placeholder in result for placeholder in ['{tmdb', '[tmdb'])

        print(f"   🎯 处理状态: {'✅ 成功' if success else '❌ 失败'}")
        print("-" * 80)

if __name__ == "__main__":
    test_tmdbid_replacement()
