<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>豆瓣 Emby 脚本 UI 预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        /* 主容器样式 - 优化尺寸 */
        #emby-status {
            background: linear-gradient(135deg, #F2F2F7 0%, #FFFFFF 100%);
            border: 1px solid #007AFF;
            border-radius: 8px;
            padding: 10px 16px;
            margin: 12px 0;
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.1);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            transition: all 0.3s ease;
        }

        #emby-status:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
        }

        /* PT站点链接容器 */
        #pt-emby-links {
            background: #F2F2F7;
            border-radius: 10px;
            padding: 16px;
            margin: 12px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* 切换按钮样式 */
        .pt-toggle-button {
            display: inline-flex;
            align-items: center;
            padding: 12px 20px;
            border-radius: 8px;
            background: #007AFF;
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 122, 255, 0.3);
            margin-bottom: 12px;
            gap: 8px;
        }

        .pt-toggle-button:hover {
            background: #0056CC;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 122, 255, 0.4);
            color: white;
            text-decoration: none;
        }

        /* PT站点按钮容器 */
        .pt-sites-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, opacity 0.3s ease-out;
            opacity: 0;
        }

        .pt-sites-container.expanded {
            max-height: 500px;
            opacity: 1;
        }

        /* 链接按钮样式 - 优化尺寸 */
        .pt-link-button {
            display: inline-flex;
            align-items: center;
            padding: 8px 14px;
            border-radius: 6px;
            background: #007AFF;
            color: white;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
            min-width: 90px;
            justify-content: center;
            gap: 6px;
        }

        .pt-link-button:hover {
            background: #0056CC;
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 122, 255, 0.4);
            color: white;
            text-decoration: none;
        }

        .pt-link-button:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px rgba(0, 122, 255, 0.3);
        }

        /* 外部链接样式区分 */
        .pt-link-button.external {
            background: #FF9500;
            box-shadow: 0 1px 3px rgba(255, 149, 0, 0.3);
        }

        .pt-link-button.external:hover {
            background: #E6850E;
            box-shadow: 0 3px 6px rgba(255, 149, 0, 0.4);
        }

        /* 状态显示样式 - 减小尺寸 */
        .emby-status-text {
            font-size: 16px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 0;
        }

        .emby-status-success {
            color: #34C759;
        }

        .emby-status-warning {
            color: #FF9500;
        }

        .emby-status-error {
            color: #FF3B30;
        }

        /* 切换图标动画 */
        .toggle-icon {
            transition: transform 0.3s ease;
        }

        .toggle-icon.rotated {
            transform: rotate(180deg);
        }

        h1 {
            color: #333;
            margin-bottom: 20px;
        }

        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>豆瓣 Emby 脚本 UI 优化预览</h1>
    
    <div class="demo-section">
        <h2>优化后的界面效果</h2>
        
        <!-- Emby状态显示 - 优化后更紧凑 -->
        <div id="emby-status">
            <div class="emby-status-text emby-status-success">
                ✅ 媒体已存在 (精确匹配)
            </div>
        </div>

        <!-- PT站点和外部链接 -->
        <div id="pt-emby-links">
            <!-- 外部链接始终显示 -->
            <a href="#" class="pt-link-button external">🎬 TMDB中文</a>
            <a href="#" class="pt-link-button external">🔍 EMBY搜索</a>
            
            <!-- PT站点切换按钮 -->
            <button class="pt-toggle-button" onclick="togglePTSites()" id="pt-toggle-btn">
                <span class="toggle-icon" id="toggle-icon">▼</span>
                PT站点 (10个)
            </button>
            
            <!-- PT站点容器 - 可折叠 -->
            <div class="pt-sites-container" id="pt-sites-container">
                <a href="#" class="pt-link-button">🥟 馒头</a>
                <a href="#" class="pt-link-button">👥 观众</a>
                <a href="#" class="pt-link-button">😊 憨憨</a>
                <a href="#" class="pt-link-button">☁️ 天空</a>
                <a href="#" class="pt-link-button">🌈 彩虹岛</a>
                <a href="#" class="pt-link-button">🎵 听听歌</a>
                <a href="#" class="pt-link-button">🏰 我堡</a>
                <a href="#" class="pt-link-button">🔊 杜比</a>
                <a href="#" class="pt-link-button">🌸 春天</a>
                <a href="#" class="pt-link-button">🏯 你堡</a>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h3>主要改进点：</h3>
        <ul>
            <li><strong>状态显示优化</strong>：字体大小从18px减小到16px，减少了内边距，视觉上更加平衡</li>
            <li><strong>PT站点按钮</strong>：保持16px字体大小，确保可读性，添加了折叠功能</li>
            <li><strong>折叠式布局</strong>：10个PT站点通过切换按钮控制显示/隐藏，避免界面拥挤</li>
            <li><strong>外部链接区分</strong>：TMDB和Emby链接使用橙色，始终可见</li>
            <li><strong>响应式设计</strong>：在移动设备上自动调整为垂直布局</li>
            <li><strong>平滑动画</strong>：展开/收起动画，图标旋转效果</li>
        </ul>
    </div>

    <script>
        // 切换PT站点显示状态
        function togglePTSites() {
            const container = document.getElementById('pt-sites-container');
            const icon = document.getElementById('toggle-icon');
            
            if (container && icon) {
                const isExpanded = container.classList.contains('expanded');
                
                if (isExpanded) {
                    container.classList.remove('expanded');
                    icon.textContent = '▼';
                    icon.classList.remove('rotated');
                } else {
                    container.classList.add('expanded');
                    icon.textContent = '▲';
                    icon.classList.add('rotated');
                }
            }
        }
    </script>
</body>
</html>
