# 豆瓣 Emby 媒体检查脚本优化说明

## 版本更新
- **版本号**: 3.2 → 3.3
- **更新日期**: 2025-06-19

## 主要优化内容

### 1. 解决「? 疑似存在」匹配逻辑优化

#### 问题分析
原先的「? 疑似存在」状态是脚本的正确设计逻辑：
- **精确匹配 (✓ 已入库)**: 使用豆瓣页面的 IMDb ID 在 Emby 中查找，100% 准确
- **模糊匹配 (? 疑似存在)**: 当 IMDb ID 匹配失败时，使用中文标题搜索，准确性不保证

#### 优化方案
1. **增强日志记录**: 添加详细的匹配过程日志，便于调试和理解匹配逻辑
2. **改进提示信息**: 更清晰地说明匹配方式和结果含义
3. **直接跳转优化**: 模糊匹配时直接跳转到第一个结果的详情页

### 2. 解决「EMBY搜索」按钮无效问题

#### 问题分析
「EMBY搜索」按钮点击后搜索框没有自动填入名称的原因：
- **反向代理问题**: Nginx、Caddy 等代理可能丢失 URL 参数
- **非标准客户端**: 第三方 Web 客户端可能不支持深链接搜索

#### 优化方案
1. **搜索链接优化**: 
   - 原来: `${embyHost}/emby/Items?...&SearchTerm=...` (API链接)
   - 现在: `${embyHost}/web/index.html#!/search.html?query=...` (Web界面链接)

2. **模糊匹配结果优化**:
   - 原来: 链接到搜索页面，可能因代理问题失效
   - 现在: 直接链接到第一个匹配结果的详情页，完全绕过搜索链接问题

### 3. 代码健壮性增强

#### 配置验证
- 添加 `validateConfig()` 函数，检查 Emby 服务器配置
- 在初始化时验证配置，提供更好的错误提示

#### 错误处理改进
- 增强错误信息的详细程度
- 添加更多调试日志，便于问题排查
- 改进缓存机制的错误处理

#### 代码注释优化
- 为关键函数添加详细注释
- 解释匹配逻辑的设计原理
- 标注优化点和解决的问题

## 技术细节

### 匹配流程优化
```javascript
// 1. 优先使用 IMDb ID 精确匹配
if (imdb_id) {
    const result = await searchEmbyByImdb(imdb_id);
    if (result.status === 'found') {
        // 精确匹配成功，显示 "✨ 媒体已收藏"
        return result;
    }
}

// 2. IMDb ID 匹配失败，使用标题模糊匹配
if (unititle) {
    const result = await searchEmbyByTitle(unititle);
    // 直接跳转到第一个结果的详情页，显示 "? 疑似存在"
    return result;
}
```

### 链接生成优化
```javascript
// Emby 搜索链接优化
const embySearchUrl = `${embyHost}/web/index.html#!/search.html?query=${encodeURIComponent(movieInfo.unititle)}`;

// 模糊匹配结果直接跳转
const jumpUrl = `${embyHost}/web/index.html#!/item?id=${firstItem.Id}&serverId=${firstItem.ServerId}`;
```

## 用户体验改进

1. **更清晰的状态提示**: 
   - "✨ 媒体已收藏" (IMDb ID 精确匹配)
   - "? 疑似存在" (标题模糊匹配)
   - "📭 暂未收藏" (未找到)

2. **更好的跳转体验**:
   - 精确匹配: 直接跳转到媒体详情页
   - 模糊匹配: 直接跳转到第一个结果的详情页
   - 搜索按钮: 跳转到 Emby Web 搜索界面

3. **增强的错误提示**:
   - 配置错误时提供具体的缺失信息
   - 网络错误时显示详细的错误原因

## 兼容性说明

- 保持与原有功能的完全兼容
- 支持各种 Emby 部署方式（直连、反向代理、域名访问）
- 兼容不同的 Emby Web 客户端和主题

## 使用建议

1. **配置检查**: 确保正确设置 `embyHost` 和 `embyApiKey`
2. **网络连接**: 确保浏览器能够访问 Emby 服务器
3. **元数据完整性**: 建议为媒体库刮削完整的 IMDb ID 信息以获得最佳匹配效果

## 配置示例

```javascript
// 必需配置
const embyHost = "http://your-emby-server:8096";  // 您的Emby服务器地址
const embyApiKey = "your-api-key-here";           // 您的Emby API密钥
```

## 故障排除

### 常见问题
1. **配置未完成**: 检查 embyHost 和 embyApiKey 是否正确设置
2. **连接失败**: 检查网络连接和 Emby 服务器状态
3. **搜索无结果**: 检查媒体库中是否存在该影片，以及元数据是否完整

### 调试方法
- 打开浏览器开发者工具查看控制台日志
- 检查网络请求是否成功
- 验证 Emby API 响应内容
